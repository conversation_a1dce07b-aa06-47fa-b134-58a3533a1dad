('E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\file_share.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('main', 'E:\\letvar\\works\\python_app\\file_share\\main.py', 'PYSOURCE'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\libtkdnd2.9.3.dll',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\libtkdnd2.9.3.dll',
   'BINARY'),
  ('tkinterdnd2\\tkdnd\\win-x64\\libtkdnd2.9.4.dll',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\libtkdnd2.9.4.dll',
   'BINARY'),
  ('tkinterdnd2\\tkdnd\\win-x86\\libtkdnd2.9.4.dll',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\libtkdnd2.9.4.dll',
   'BINARY'),
  ('python312.dll', 'E:\\letvar\\apps\\Python312\\python312.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('select.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\etree.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\_elementpath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\sax.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\objectify.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\html\\diff.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\html\\_difflib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\builder.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'E:\\letvar\\apps\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('netifaces.cp312-win_amd64.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\netifaces.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'E:\\letvar\\apps\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('win32\\win32service.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\win32service.pyd',
   'EXTENSION'),
  ('win32\\perfmon.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\perfmon.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\servicemanager.pyd',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\win32\\servicemanager.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'E:\\letvar\\apps\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'E:\\letvar\\apps\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'E:\\letvar\\apps\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll', 'E:\\letvar\\apps\\Python312\\python3.dll', 'BINARY'),
  ('libssl-3.dll', 'E:\\letvar\\apps\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('tcl86t.dll', 'E:\\letvar\\apps\\Python312\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'E:\\letvar\\apps\\Python312\\DLLs\\tk86t.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\letvar\\apps\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('ucrtbase.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'E:\\letvar\\apps\\Python312\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\letvar\\apps\\Java\\jdk-1.8\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('static\\ace\\ace.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ace.js',
   'DATA'),
  ('static\\ace\\ext-beautify.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-beautify.js',
   'DATA'),
  ('static\\ace\\ext-code_lens.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-code_lens.js',
   'DATA'),
  ('static\\ace\\ext-command_bar.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-command_bar.js',
   'DATA'),
  ('static\\ace\\ext-elastic_tabstops_lite.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-elastic_tabstops_lite.js',
   'DATA'),
  ('static\\ace\\ext-emmet.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-emmet.js',
   'DATA'),
  ('static\\ace\\ext-error_marker.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-error_marker.js',
   'DATA'),
  ('static\\ace\\ext-hardwrap.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-hardwrap.js',
   'DATA'),
  ('static\\ace\\ext-inline_autocomplete.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-inline_autocomplete.js',
   'DATA'),
  ('static\\ace\\ext-keybinding_menu.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-keybinding_menu.js',
   'DATA'),
  ('static\\ace\\ext-language_tools.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-language_tools.js',
   'DATA'),
  ('static\\ace\\ext-linking.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-linking.js',
   'DATA'),
  ('static\\ace\\ext-modelist.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-modelist.js',
   'DATA'),
  ('static\\ace\\ext-options.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-options.js',
   'DATA'),
  ('static\\ace\\ext-prompt.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-prompt.js',
   'DATA'),
  ('static\\ace\\ext-rtl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-rtl.js',
   'DATA'),
  ('static\\ace\\ext-searchbox.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-searchbox.js',
   'DATA'),
  ('static\\ace\\ext-settings_menu.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-settings_menu.js',
   'DATA'),
  ('static\\ace\\ext-simple_tokenizer.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-simple_tokenizer.js',
   'DATA'),
  ('static\\ace\\ext-spellcheck.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-spellcheck.js',
   'DATA'),
  ('static\\ace\\ext-split.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-split.js',
   'DATA'),
  ('static\\ace\\ext-static_highlight.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-static_highlight.js',
   'DATA'),
  ('static\\ace\\ext-statusbar.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-statusbar.js',
   'DATA'),
  ('static\\ace\\ext-textarea.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-textarea.js',
   'DATA'),
  ('static\\ace\\ext-themelist.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-themelist.js',
   'DATA'),
  ('static\\ace\\ext-whitespace.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\ext-whitespace.js',
   'DATA'),
  ('static\\ace\\keybinding-emacs.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\keybinding-emacs.js',
   'DATA'),
  ('static\\ace\\keybinding-sublime.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\keybinding-sublime.js',
   'DATA'),
  ('static\\ace\\keybinding-vim.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\keybinding-vim.js',
   'DATA'),
  ('static\\ace\\keybinding-vscode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\keybinding-vscode.js',
   'DATA'),
  ('static\\ace\\mode-abap.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-abap.js',
   'DATA'),
  ('static\\ace\\mode-abc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-abc.js',
   'DATA'),
  ('static\\ace\\mode-actionscript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-actionscript.js',
   'DATA'),
  ('static\\ace\\mode-ada.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ada.js',
   'DATA'),
  ('static\\ace\\mode-alda.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-alda.js',
   'DATA'),
  ('static\\ace\\mode-apache_conf.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-apache_conf.js',
   'DATA'),
  ('static\\ace\\mode-apex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-apex.js',
   'DATA'),
  ('static\\ace\\mode-applescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-applescript.js',
   'DATA'),
  ('static\\ace\\mode-aql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-aql.js',
   'DATA'),
  ('static\\ace\\mode-asciidoc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-asciidoc.js',
   'DATA'),
  ('static\\ace\\mode-asl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-asl.js',
   'DATA'),
  ('static\\ace\\mode-assembly_arm32.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-assembly_arm32.js',
   'DATA'),
  ('static\\ace\\mode-assembly_x86.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-assembly_x86.js',
   'DATA'),
  ('static\\ace\\mode-astro.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-astro.js',
   'DATA'),
  ('static\\ace\\mode-autohotkey.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-autohotkey.js',
   'DATA'),
  ('static\\ace\\mode-basic.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-basic.js',
   'DATA'),
  ('static\\ace\\mode-batchfile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-batchfile.js',
   'DATA'),
  ('static\\ace\\mode-bibtex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-bibtex.js',
   'DATA'),
  ('static\\ace\\mode-c9search.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-c9search.js',
   'DATA'),
  ('static\\ace\\mode-c_cpp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-c_cpp.js',
   'DATA'),
  ('static\\ace\\mode-cirru.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-cirru.js',
   'DATA'),
  ('static\\ace\\mode-clojure.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-clojure.js',
   'DATA'),
  ('static\\ace\\mode-cobol.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-cobol.js',
   'DATA'),
  ('static\\ace\\mode-coffee.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-coffee.js',
   'DATA'),
  ('static\\ace\\mode-coldfusion.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-coldfusion.js',
   'DATA'),
  ('static\\ace\\mode-crystal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-crystal.js',
   'DATA'),
  ('static\\ace\\mode-csharp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-csharp.js',
   'DATA'),
  ('static\\ace\\mode-csound_document.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-csound_document.js',
   'DATA'),
  ('static\\ace\\mode-csound_orchestra.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-csound_orchestra.js',
   'DATA'),
  ('static\\ace\\mode-csound_score.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-csound_score.js',
   'DATA'),
  ('static\\ace\\mode-csp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-csp.js',
   'DATA'),
  ('static\\ace\\mode-css.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-css.js',
   'DATA'),
  ('static\\ace\\mode-curly.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-curly.js',
   'DATA'),
  ('static\\ace\\mode-cuttlefish.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-cuttlefish.js',
   'DATA'),
  ('static\\ace\\mode-d.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-d.js',
   'DATA'),
  ('static\\ace\\mode-dart.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-dart.js',
   'DATA'),
  ('static\\ace\\mode-diff.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-diff.js',
   'DATA'),
  ('static\\ace\\mode-django.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-django.js',
   'DATA'),
  ('static\\ace\\mode-dockerfile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-dockerfile.js',
   'DATA'),
  ('static\\ace\\mode-dot.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-dot.js',
   'DATA'),
  ('static\\ace\\mode-drools.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-drools.js',
   'DATA'),
  ('static\\ace\\mode-edifact.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-edifact.js',
   'DATA'),
  ('static\\ace\\mode-eiffel.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-eiffel.js',
   'DATA'),
  ('static\\ace\\mode-ejs.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ejs.js',
   'DATA'),
  ('static\\ace\\mode-elixir.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-elixir.js',
   'DATA'),
  ('static\\ace\\mode-elm.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-elm.js',
   'DATA'),
  ('static\\ace\\mode-erlang.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-erlang.js',
   'DATA'),
  ('static\\ace\\mode-flix.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-flix.js',
   'DATA'),
  ('static\\ace\\mode-forth.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-forth.js',
   'DATA'),
  ('static\\ace\\mode-fortran.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-fortran.js',
   'DATA'),
  ('static\\ace\\mode-fsharp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-fsharp.js',
   'DATA'),
  ('static\\ace\\mode-fsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-fsl.js',
   'DATA'),
  ('static\\ace\\mode-ftl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ftl.js',
   'DATA'),
  ('static\\ace\\mode-gcode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-gcode.js',
   'DATA'),
  ('static\\ace\\mode-gherkin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-gherkin.js',
   'DATA'),
  ('static\\ace\\mode-gitignore.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-gitignore.js',
   'DATA'),
  ('static\\ace\\mode-glsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-glsl.js',
   'DATA'),
  ('static\\ace\\mode-gobstones.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-gobstones.js',
   'DATA'),
  ('static\\ace\\mode-golang.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-golang.js',
   'DATA'),
  ('static\\ace\\mode-graphqlschema.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-graphqlschema.js',
   'DATA'),
  ('static\\ace\\mode-groovy.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-groovy.js',
   'DATA'),
  ('static\\ace\\mode-haml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-haml.js',
   'DATA'),
  ('static\\ace\\mode-handlebars.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-handlebars.js',
   'DATA'),
  ('static\\ace\\mode-haskell.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-haskell.js',
   'DATA'),
  ('static\\ace\\mode-haskell_cabal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-haskell_cabal.js',
   'DATA'),
  ('static\\ace\\mode-haxe.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-haxe.js',
   'DATA'),
  ('static\\ace\\mode-hjson.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-hjson.js',
   'DATA'),
  ('static\\ace\\mode-html.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-html.js',
   'DATA'),
  ('static\\ace\\mode-html_elixir.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-html_elixir.js',
   'DATA'),
  ('static\\ace\\mode-html_ruby.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-html_ruby.js',
   'DATA'),
  ('static\\ace\\mode-ini.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ini.js',
   'DATA'),
  ('static\\ace\\mode-io.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-io.js',
   'DATA'),
  ('static\\ace\\mode-ion.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ion.js',
   'DATA'),
  ('static\\ace\\mode-jack.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jack.js',
   'DATA'),
  ('static\\ace\\mode-jade.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jade.js',
   'DATA'),
  ('static\\ace\\mode-java.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-java.js',
   'DATA'),
  ('static\\ace\\mode-javascript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-javascript.js',
   'DATA'),
  ('static\\ace\\mode-jexl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jexl.js',
   'DATA'),
  ('static\\ace\\mode-json.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-json.js',
   'DATA'),
  ('static\\ace\\mode-json5.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-json5.js',
   'DATA'),
  ('static\\ace\\mode-jsoniq.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jsoniq.js',
   'DATA'),
  ('static\\ace\\mode-jsp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jsp.js',
   'DATA'),
  ('static\\ace\\mode-jssm.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jssm.js',
   'DATA'),
  ('static\\ace\\mode-jsx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-jsx.js',
   'DATA'),
  ('static\\ace\\mode-julia.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-julia.js',
   'DATA'),
  ('static\\ace\\mode-kotlin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-kotlin.js',
   'DATA'),
  ('static\\ace\\mode-latex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-latex.js',
   'DATA'),
  ('static\\ace\\mode-latte.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-latte.js',
   'DATA'),
  ('static\\ace\\mode-less.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-less.js',
   'DATA'),
  ('static\\ace\\mode-liquid.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-liquid.js',
   'DATA'),
  ('static\\ace\\mode-lisp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-lisp.js',
   'DATA'),
  ('static\\ace\\mode-livescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-livescript.js',
   'DATA'),
  ('static\\ace\\mode-logiql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-logiql.js',
   'DATA'),
  ('static\\ace\\mode-logtalk.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-logtalk.js',
   'DATA'),
  ('static\\ace\\mode-lsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-lsl.js',
   'DATA'),
  ('static\\ace\\mode-lua.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-lua.js',
   'DATA'),
  ('static\\ace\\mode-luapage.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-luapage.js',
   'DATA'),
  ('static\\ace\\mode-lucene.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-lucene.js',
   'DATA'),
  ('static\\ace\\mode-makefile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-makefile.js',
   'DATA'),
  ('static\\ace\\mode-markdown.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-markdown.js',
   'DATA'),
  ('static\\ace\\mode-mask.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mask.js',
   'DATA'),
  ('static\\ace\\mode-matlab.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-matlab.js',
   'DATA'),
  ('static\\ace\\mode-maze.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-maze.js',
   'DATA'),
  ('static\\ace\\mode-mediawiki.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mediawiki.js',
   'DATA'),
  ('static\\ace\\mode-mel.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mel.js',
   'DATA'),
  ('static\\ace\\mode-mips.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mips.js',
   'DATA'),
  ('static\\ace\\mode-mixal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mixal.js',
   'DATA'),
  ('static\\ace\\mode-mushcode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mushcode.js',
   'DATA'),
  ('static\\ace\\mode-mysql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-mysql.js',
   'DATA'),
  ('static\\ace\\mode-nasal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nasal.js',
   'DATA'),
  ('static\\ace\\mode-nginx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nginx.js',
   'DATA'),
  ('static\\ace\\mode-nim.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nim.js',
   'DATA'),
  ('static\\ace\\mode-nix.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nix.js',
   'DATA'),
  ('static\\ace\\mode-nsis.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nsis.js',
   'DATA'),
  ('static\\ace\\mode-nunjucks.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-nunjucks.js',
   'DATA'),
  ('static\\ace\\mode-objectivec.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-objectivec.js',
   'DATA'),
  ('static\\ace\\mode-ocaml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ocaml.js',
   'DATA'),
  ('static\\ace\\mode-odin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-odin.js',
   'DATA'),
  ('static\\ace\\mode-partiql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-partiql.js',
   'DATA'),
  ('static\\ace\\mode-pascal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-pascal.js',
   'DATA'),
  ('static\\ace\\mode-perl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-perl.js',
   'DATA'),
  ('static\\ace\\mode-pgsql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-pgsql.js',
   'DATA'),
  ('static\\ace\\mode-php.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-php.js',
   'DATA'),
  ('static\\ace\\mode-php_laravel_blade.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-php_laravel_blade.js',
   'DATA'),
  ('static\\ace\\mode-pig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-pig.js',
   'DATA'),
  ('static\\ace\\mode-plain_text.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-plain_text.js',
   'DATA'),
  ('static\\ace\\mode-plsql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-plsql.js',
   'DATA'),
  ('static\\ace\\mode-powershell.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-powershell.js',
   'DATA'),
  ('static\\ace\\mode-praat.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-praat.js',
   'DATA'),
  ('static\\ace\\mode-prisma.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-prisma.js',
   'DATA'),
  ('static\\ace\\mode-prolog.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-prolog.js',
   'DATA'),
  ('static\\ace\\mode-properties.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-properties.js',
   'DATA'),
  ('static\\ace\\mode-protobuf.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-protobuf.js',
   'DATA'),
  ('static\\ace\\mode-prql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-prql.js',
   'DATA'),
  ('static\\ace\\mode-puppet.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-puppet.js',
   'DATA'),
  ('static\\ace\\mode-python.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-python.js',
   'DATA'),
  ('static\\ace\\mode-qml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-qml.js',
   'DATA'),
  ('static\\ace\\mode-r.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-r.js',
   'DATA'),
  ('static\\ace\\mode-raku.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-raku.js',
   'DATA'),
  ('static\\ace\\mode-razor.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-razor.js',
   'DATA'),
  ('static\\ace\\mode-rdoc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-rdoc.js',
   'DATA'),
  ('static\\ace\\mode-red.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-red.js',
   'DATA'),
  ('static\\ace\\mode-redshift.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-redshift.js',
   'DATA'),
  ('static\\ace\\mode-rhtml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-rhtml.js',
   'DATA'),
  ('static\\ace\\mode-robot.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-robot.js',
   'DATA'),
  ('static\\ace\\mode-rst.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-rst.js',
   'DATA'),
  ('static\\ace\\mode-ruby.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-ruby.js',
   'DATA'),
  ('static\\ace\\mode-rust.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-rust.js',
   'DATA'),
  ('static\\ace\\mode-sac.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sac.js',
   'DATA'),
  ('static\\ace\\mode-sass.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sass.js',
   'DATA'),
  ('static\\ace\\mode-scad.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-scad.js',
   'DATA'),
  ('static\\ace\\mode-scala.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-scala.js',
   'DATA'),
  ('static\\ace\\mode-scheme.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-scheme.js',
   'DATA'),
  ('static\\ace\\mode-scrypt.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-scrypt.js',
   'DATA'),
  ('static\\ace\\mode-scss.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-scss.js',
   'DATA'),
  ('static\\ace\\mode-sh.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sh.js',
   'DATA'),
  ('static\\ace\\mode-sjs.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sjs.js',
   'DATA'),
  ('static\\ace\\mode-slim.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-slim.js',
   'DATA'),
  ('static\\ace\\mode-smarty.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-smarty.js',
   'DATA'),
  ('static\\ace\\mode-smithy.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-smithy.js',
   'DATA'),
  ('static\\ace\\mode-snippets.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-snippets.js',
   'DATA'),
  ('static\\ace\\mode-soy_template.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-soy_template.js',
   'DATA'),
  ('static\\ace\\mode-space.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-space.js',
   'DATA'),
  ('static\\ace\\mode-sparql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sparql.js',
   'DATA'),
  ('static\\ace\\mode-sql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sql.js',
   'DATA'),
  ('static\\ace\\mode-sqlserver.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-sqlserver.js',
   'DATA'),
  ('static\\ace\\mode-stylus.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-stylus.js',
   'DATA'),
  ('static\\ace\\mode-svg.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-svg.js',
   'DATA'),
  ('static\\ace\\mode-swift.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-swift.js',
   'DATA'),
  ('static\\ace\\mode-tcl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-tcl.js',
   'DATA'),
  ('static\\ace\\mode-terraform.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-terraform.js',
   'DATA'),
  ('static\\ace\\mode-tex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-tex.js',
   'DATA'),
  ('static\\ace\\mode-text.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-text.js',
   'DATA'),
  ('static\\ace\\mode-textile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-textile.js',
   'DATA'),
  ('static\\ace\\mode-toml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-toml.js',
   'DATA'),
  ('static\\ace\\mode-tsx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-tsx.js',
   'DATA'),
  ('static\\ace\\mode-turtle.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-turtle.js',
   'DATA'),
  ('static\\ace\\mode-twig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-twig.js',
   'DATA'),
  ('static\\ace\\mode-typescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-typescript.js',
   'DATA'),
  ('static\\ace\\mode-vala.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-vala.js',
   'DATA'),
  ('static\\ace\\mode-vbscript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-vbscript.js',
   'DATA'),
  ('static\\ace\\mode-velocity.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-velocity.js',
   'DATA'),
  ('static\\ace\\mode-verilog.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-verilog.js',
   'DATA'),
  ('static\\ace\\mode-vhdl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-vhdl.js',
   'DATA'),
  ('static\\ace\\mode-visualforce.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-visualforce.js',
   'DATA'),
  ('static\\ace\\mode-vue.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-vue.js',
   'DATA'),
  ('static\\ace\\mode-wollok.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-wollok.js',
   'DATA'),
  ('static\\ace\\mode-xml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-xml.js',
   'DATA'),
  ('static\\ace\\mode-xquery.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-xquery.js',
   'DATA'),
  ('static\\ace\\mode-yaml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-yaml.js',
   'DATA'),
  ('static\\ace\\mode-zeek.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-zeek.js',
   'DATA'),
  ('static\\ace\\mode-zig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\mode-zig.js',
   'DATA'),
  ('static\\ace\\snippets\\abap.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\abap.js',
   'DATA'),
  ('static\\ace\\snippets\\abc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\abc.js',
   'DATA'),
  ('static\\ace\\snippets\\actionscript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\actionscript.js',
   'DATA'),
  ('static\\ace\\snippets\\ada.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ada.js',
   'DATA'),
  ('static\\ace\\snippets\\alda.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\alda.js',
   'DATA'),
  ('static\\ace\\snippets\\apache_conf.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\apache_conf.js',
   'DATA'),
  ('static\\ace\\snippets\\apex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\apex.js',
   'DATA'),
  ('static\\ace\\snippets\\applescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\applescript.js',
   'DATA'),
  ('static\\ace\\snippets\\aql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\aql.js',
   'DATA'),
  ('static\\ace\\snippets\\asciidoc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\asciidoc.js',
   'DATA'),
  ('static\\ace\\snippets\\asl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\asl.js',
   'DATA'),
  ('static\\ace\\snippets\\assembly_arm32.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\assembly_arm32.js',
   'DATA'),
  ('static\\ace\\snippets\\assembly_x86.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\assembly_x86.js',
   'DATA'),
  ('static\\ace\\snippets\\astro.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\astro.js',
   'DATA'),
  ('static\\ace\\snippets\\autohotkey.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\autohotkey.js',
   'DATA'),
  ('static\\ace\\snippets\\basic.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\basic.js',
   'DATA'),
  ('static\\ace\\snippets\\batchfile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\batchfile.js',
   'DATA'),
  ('static\\ace\\snippets\\bibtex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\bibtex.js',
   'DATA'),
  ('static\\ace\\snippets\\c9search.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\c9search.js',
   'DATA'),
  ('static\\ace\\snippets\\c_cpp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\c_cpp.js',
   'DATA'),
  ('static\\ace\\snippets\\cirru.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\cirru.js',
   'DATA'),
  ('static\\ace\\snippets\\clojure.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\clojure.js',
   'DATA'),
  ('static\\ace\\snippets\\cobol.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\cobol.js',
   'DATA'),
  ('static\\ace\\snippets\\coffee.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\coffee.js',
   'DATA'),
  ('static\\ace\\snippets\\coldfusion.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\coldfusion.js',
   'DATA'),
  ('static\\ace\\snippets\\crystal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\crystal.js',
   'DATA'),
  ('static\\ace\\snippets\\csharp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\csharp.js',
   'DATA'),
  ('static\\ace\\snippets\\csound_document.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\csound_document.js',
   'DATA'),
  ('static\\ace\\snippets\\csound_orchestra.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\csound_orchestra.js',
   'DATA'),
  ('static\\ace\\snippets\\csound_score.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\csound_score.js',
   'DATA'),
  ('static\\ace\\snippets\\csp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\csp.js',
   'DATA'),
  ('static\\ace\\snippets\\css.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\css.js',
   'DATA'),
  ('static\\ace\\snippets\\curly.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\curly.js',
   'DATA'),
  ('static\\ace\\snippets\\cuttlefish.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\cuttlefish.js',
   'DATA'),
  ('static\\ace\\snippets\\d.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\d.js',
   'DATA'),
  ('static\\ace\\snippets\\dart.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\dart.js',
   'DATA'),
  ('static\\ace\\snippets\\diff.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\diff.js',
   'DATA'),
  ('static\\ace\\snippets\\django.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\django.js',
   'DATA'),
  ('static\\ace\\snippets\\dockerfile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\dockerfile.js',
   'DATA'),
  ('static\\ace\\snippets\\dot.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\dot.js',
   'DATA'),
  ('static\\ace\\snippets\\drools.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\drools.js',
   'DATA'),
  ('static\\ace\\snippets\\edifact.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\edifact.js',
   'DATA'),
  ('static\\ace\\snippets\\eiffel.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\eiffel.js',
   'DATA'),
  ('static\\ace\\snippets\\ejs.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ejs.js',
   'DATA'),
  ('static\\ace\\snippets\\elixir.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\elixir.js',
   'DATA'),
  ('static\\ace\\snippets\\elm.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\elm.js',
   'DATA'),
  ('static\\ace\\snippets\\erlang.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\erlang.js',
   'DATA'),
  ('static\\ace\\snippets\\flix.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\flix.js',
   'DATA'),
  ('static\\ace\\snippets\\forth.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\forth.js',
   'DATA'),
  ('static\\ace\\snippets\\fortran.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\fortran.js',
   'DATA'),
  ('static\\ace\\snippets\\fsharp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\fsharp.js',
   'DATA'),
  ('static\\ace\\snippets\\fsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\fsl.js',
   'DATA'),
  ('static\\ace\\snippets\\ftl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ftl.js',
   'DATA'),
  ('static\\ace\\snippets\\gcode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\gcode.js',
   'DATA'),
  ('static\\ace\\snippets\\gherkin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\gherkin.js',
   'DATA'),
  ('static\\ace\\snippets\\gitignore.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\gitignore.js',
   'DATA'),
  ('static\\ace\\snippets\\glsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\glsl.js',
   'DATA'),
  ('static\\ace\\snippets\\gobstones.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\gobstones.js',
   'DATA'),
  ('static\\ace\\snippets\\golang.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\golang.js',
   'DATA'),
  ('static\\ace\\snippets\\graphqlschema.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\graphqlschema.js',
   'DATA'),
  ('static\\ace\\snippets\\groovy.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\groovy.js',
   'DATA'),
  ('static\\ace\\snippets\\haml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\haml.js',
   'DATA'),
  ('static\\ace\\snippets\\handlebars.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\handlebars.js',
   'DATA'),
  ('static\\ace\\snippets\\haskell.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\haskell.js',
   'DATA'),
  ('static\\ace\\snippets\\haskell_cabal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\haskell_cabal.js',
   'DATA'),
  ('static\\ace\\snippets\\haxe.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\haxe.js',
   'DATA'),
  ('static\\ace\\snippets\\hjson.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\hjson.js',
   'DATA'),
  ('static\\ace\\snippets\\html.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\html.js',
   'DATA'),
  ('static\\ace\\snippets\\html_elixir.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\html_elixir.js',
   'DATA'),
  ('static\\ace\\snippets\\html_ruby.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\html_ruby.js',
   'DATA'),
  ('static\\ace\\snippets\\ini.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ini.js',
   'DATA'),
  ('static\\ace\\snippets\\io.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\io.js',
   'DATA'),
  ('static\\ace\\snippets\\ion.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ion.js',
   'DATA'),
  ('static\\ace\\snippets\\jack.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jack.js',
   'DATA'),
  ('static\\ace\\snippets\\jade.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jade.js',
   'DATA'),
  ('static\\ace\\snippets\\java.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\java.js',
   'DATA'),
  ('static\\ace\\snippets\\javascript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\javascript.js',
   'DATA'),
  ('static\\ace\\snippets\\jexl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jexl.js',
   'DATA'),
  ('static\\ace\\snippets\\json.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\json.js',
   'DATA'),
  ('static\\ace\\snippets\\json5.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\json5.js',
   'DATA'),
  ('static\\ace\\snippets\\jsoniq.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jsoniq.js',
   'DATA'),
  ('static\\ace\\snippets\\jsp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jsp.js',
   'DATA'),
  ('static\\ace\\snippets\\jssm.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jssm.js',
   'DATA'),
  ('static\\ace\\snippets\\jsx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\jsx.js',
   'DATA'),
  ('static\\ace\\snippets\\julia.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\julia.js',
   'DATA'),
  ('static\\ace\\snippets\\kotlin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\kotlin.js',
   'DATA'),
  ('static\\ace\\snippets\\latex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\latex.js',
   'DATA'),
  ('static\\ace\\snippets\\latte.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\latte.js',
   'DATA'),
  ('static\\ace\\snippets\\less.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\less.js',
   'DATA'),
  ('static\\ace\\snippets\\liquid.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\liquid.js',
   'DATA'),
  ('static\\ace\\snippets\\lisp.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\lisp.js',
   'DATA'),
  ('static\\ace\\snippets\\livescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\livescript.js',
   'DATA'),
  ('static\\ace\\snippets\\logiql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\logiql.js',
   'DATA'),
  ('static\\ace\\snippets\\logtalk.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\logtalk.js',
   'DATA'),
  ('static\\ace\\snippets\\lsl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\lsl.js',
   'DATA'),
  ('static\\ace\\snippets\\lua.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\lua.js',
   'DATA'),
  ('static\\ace\\snippets\\luapage.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\luapage.js',
   'DATA'),
  ('static\\ace\\snippets\\lucene.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\lucene.js',
   'DATA'),
  ('static\\ace\\snippets\\makefile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\makefile.js',
   'DATA'),
  ('static\\ace\\snippets\\markdown.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\markdown.js',
   'DATA'),
  ('static\\ace\\snippets\\mask.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mask.js',
   'DATA'),
  ('static\\ace\\snippets\\matlab.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\matlab.js',
   'DATA'),
  ('static\\ace\\snippets\\maze.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\maze.js',
   'DATA'),
  ('static\\ace\\snippets\\mediawiki.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mediawiki.js',
   'DATA'),
  ('static\\ace\\snippets\\mel.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mel.js',
   'DATA'),
  ('static\\ace\\snippets\\mips.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mips.js',
   'DATA'),
  ('static\\ace\\snippets\\mixal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mixal.js',
   'DATA'),
  ('static\\ace\\snippets\\mushcode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mushcode.js',
   'DATA'),
  ('static\\ace\\snippets\\mysql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\mysql.js',
   'DATA'),
  ('static\\ace\\snippets\\nasal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nasal.js',
   'DATA'),
  ('static\\ace\\snippets\\nginx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nginx.js',
   'DATA'),
  ('static\\ace\\snippets\\nim.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nim.js',
   'DATA'),
  ('static\\ace\\snippets\\nix.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nix.js',
   'DATA'),
  ('static\\ace\\snippets\\nsis.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nsis.js',
   'DATA'),
  ('static\\ace\\snippets\\nunjucks.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\nunjucks.js',
   'DATA'),
  ('static\\ace\\snippets\\objectivec.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\objectivec.js',
   'DATA'),
  ('static\\ace\\snippets\\ocaml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ocaml.js',
   'DATA'),
  ('static\\ace\\snippets\\odin.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\odin.js',
   'DATA'),
  ('static\\ace\\snippets\\partiql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\partiql.js',
   'DATA'),
  ('static\\ace\\snippets\\pascal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\pascal.js',
   'DATA'),
  ('static\\ace\\snippets\\perl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\perl.js',
   'DATA'),
  ('static\\ace\\snippets\\pgsql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\pgsql.js',
   'DATA'),
  ('static\\ace\\snippets\\php.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\php.js',
   'DATA'),
  ('static\\ace\\snippets\\php_laravel_blade.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\php_laravel_blade.js',
   'DATA'),
  ('static\\ace\\snippets\\pig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\pig.js',
   'DATA'),
  ('static\\ace\\snippets\\plain_text.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\plain_text.js',
   'DATA'),
  ('static\\ace\\snippets\\plsql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\plsql.js',
   'DATA'),
  ('static\\ace\\snippets\\powershell.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\powershell.js',
   'DATA'),
  ('static\\ace\\snippets\\praat.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\praat.js',
   'DATA'),
  ('static\\ace\\snippets\\prisma.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\prisma.js',
   'DATA'),
  ('static\\ace\\snippets\\prolog.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\prolog.js',
   'DATA'),
  ('static\\ace\\snippets\\properties.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\properties.js',
   'DATA'),
  ('static\\ace\\snippets\\protobuf.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\protobuf.js',
   'DATA'),
  ('static\\ace\\snippets\\prql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\prql.js',
   'DATA'),
  ('static\\ace\\snippets\\puppet.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\puppet.js',
   'DATA'),
  ('static\\ace\\snippets\\python.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\python.js',
   'DATA'),
  ('static\\ace\\snippets\\qml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\qml.js',
   'DATA'),
  ('static\\ace\\snippets\\r.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\r.js',
   'DATA'),
  ('static\\ace\\snippets\\raku.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\raku.js',
   'DATA'),
  ('static\\ace\\snippets\\razor.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\razor.js',
   'DATA'),
  ('static\\ace\\snippets\\rdoc.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\rdoc.js',
   'DATA'),
  ('static\\ace\\snippets\\red.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\red.js',
   'DATA'),
  ('static\\ace\\snippets\\redshift.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\redshift.js',
   'DATA'),
  ('static\\ace\\snippets\\rhtml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\rhtml.js',
   'DATA'),
  ('static\\ace\\snippets\\robot.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\robot.js',
   'DATA'),
  ('static\\ace\\snippets\\rst.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\rst.js',
   'DATA'),
  ('static\\ace\\snippets\\ruby.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\ruby.js',
   'DATA'),
  ('static\\ace\\snippets\\rust.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\rust.js',
   'DATA'),
  ('static\\ace\\snippets\\sac.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sac.js',
   'DATA'),
  ('static\\ace\\snippets\\sass.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sass.js',
   'DATA'),
  ('static\\ace\\snippets\\scad.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\scad.js',
   'DATA'),
  ('static\\ace\\snippets\\scala.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\scala.js',
   'DATA'),
  ('static\\ace\\snippets\\scheme.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\scheme.js',
   'DATA'),
  ('static\\ace\\snippets\\scrypt.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\scrypt.js',
   'DATA'),
  ('static\\ace\\snippets\\scss.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\scss.js',
   'DATA'),
  ('static\\ace\\snippets\\sh.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sh.js',
   'DATA'),
  ('static\\ace\\snippets\\sjs.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sjs.js',
   'DATA'),
  ('static\\ace\\snippets\\slim.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\slim.js',
   'DATA'),
  ('static\\ace\\snippets\\smarty.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\smarty.js',
   'DATA'),
  ('static\\ace\\snippets\\smithy.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\smithy.js',
   'DATA'),
  ('static\\ace\\snippets\\snippets.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\snippets.js',
   'DATA'),
  ('static\\ace\\snippets\\soy_template.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\soy_template.js',
   'DATA'),
  ('static\\ace\\snippets\\space.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\space.js',
   'DATA'),
  ('static\\ace\\snippets\\sparql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sparql.js',
   'DATA'),
  ('static\\ace\\snippets\\sql.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sql.js',
   'DATA'),
  ('static\\ace\\snippets\\sqlserver.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\sqlserver.js',
   'DATA'),
  ('static\\ace\\snippets\\stylus.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\stylus.js',
   'DATA'),
  ('static\\ace\\snippets\\svg.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\svg.js',
   'DATA'),
  ('static\\ace\\snippets\\swift.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\swift.js',
   'DATA'),
  ('static\\ace\\snippets\\tcl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\tcl.js',
   'DATA'),
  ('static\\ace\\snippets\\terraform.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\terraform.js',
   'DATA'),
  ('static\\ace\\snippets\\tex.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\tex.js',
   'DATA'),
  ('static\\ace\\snippets\\text.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\text.js',
   'DATA'),
  ('static\\ace\\snippets\\textile.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\textile.js',
   'DATA'),
  ('static\\ace\\snippets\\toml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\toml.js',
   'DATA'),
  ('static\\ace\\snippets\\tsx.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\tsx.js',
   'DATA'),
  ('static\\ace\\snippets\\turtle.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\turtle.js',
   'DATA'),
  ('static\\ace\\snippets\\twig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\twig.js',
   'DATA'),
  ('static\\ace\\snippets\\typescript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\typescript.js',
   'DATA'),
  ('static\\ace\\snippets\\vala.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\vala.js',
   'DATA'),
  ('static\\ace\\snippets\\vbscript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\vbscript.js',
   'DATA'),
  ('static\\ace\\snippets\\velocity.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\velocity.js',
   'DATA'),
  ('static\\ace\\snippets\\verilog.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\verilog.js',
   'DATA'),
  ('static\\ace\\snippets\\vhdl.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\vhdl.js',
   'DATA'),
  ('static\\ace\\snippets\\visualforce.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\visualforce.js',
   'DATA'),
  ('static\\ace\\snippets\\vue.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\vue.js',
   'DATA'),
  ('static\\ace\\snippets\\wollok.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\wollok.js',
   'DATA'),
  ('static\\ace\\snippets\\xml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\xml.js',
   'DATA'),
  ('static\\ace\\snippets\\xquery.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\xquery.js',
   'DATA'),
  ('static\\ace\\snippets\\yaml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\yaml.js',
   'DATA'),
  ('static\\ace\\snippets\\zeek.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\zeek.js',
   'DATA'),
  ('static\\ace\\snippets\\zig.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\snippets\\zig.js',
   'DATA'),
  ('static\\ace\\theme-ambiance.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-ambiance.js',
   'DATA'),
  ('static\\ace\\theme-chaos.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-chaos.js',
   'DATA'),
  ('static\\ace\\theme-chrome.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-chrome.js',
   'DATA'),
  ('static\\ace\\theme-cloud9_day.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cloud9_day.js',
   'DATA'),
  ('static\\ace\\theme-cloud9_night.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cloud9_night.js',
   'DATA'),
  ('static\\ace\\theme-cloud9_night_low_color.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cloud9_night_low_color.js',
   'DATA'),
  ('static\\ace\\theme-cloud_editor.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cloud_editor.js',
   'DATA'),
  ('static\\ace\\theme-cloud_editor_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cloud_editor_dark.js',
   'DATA'),
  ('static\\ace\\theme-clouds.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-clouds.js',
   'DATA'),
  ('static\\ace\\theme-clouds_midnight.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-clouds_midnight.js',
   'DATA'),
  ('static\\ace\\theme-cobalt.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-cobalt.js',
   'DATA'),
  ('static\\ace\\theme-crimson_editor.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-crimson_editor.js',
   'DATA'),
  ('static\\ace\\theme-dawn.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-dawn.js',
   'DATA'),
  ('static\\ace\\theme-dracula.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-dracula.js',
   'DATA'),
  ('static\\ace\\theme-dreamweaver.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-dreamweaver.js',
   'DATA'),
  ('static\\ace\\theme-eclipse.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-eclipse.js',
   'DATA'),
  ('static\\ace\\theme-github.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-github.js',
   'DATA'),
  ('static\\ace\\theme-github_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-github_dark.js',
   'DATA'),
  ('static\\ace\\theme-github_light_default.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-github_light_default.js',
   'DATA'),
  ('static\\ace\\theme-gob.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-gob.js',
   'DATA'),
  ('static\\ace\\theme-gruvbox.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-gruvbox.js',
   'DATA'),
  ('static\\ace\\theme-gruvbox_dark_hard.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-gruvbox_dark_hard.js',
   'DATA'),
  ('static\\ace\\theme-gruvbox_light_hard.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-gruvbox_light_hard.js',
   'DATA'),
  ('static\\ace\\theme-idle_fingers.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-idle_fingers.js',
   'DATA'),
  ('static\\ace\\theme-iplastic.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-iplastic.js',
   'DATA'),
  ('static\\ace\\theme-katzenmilch.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-katzenmilch.js',
   'DATA'),
  ('static\\ace\\theme-kr_theme.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-kr_theme.js',
   'DATA'),
  ('static\\ace\\theme-kuroir.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-kuroir.js',
   'DATA'),
  ('static\\ace\\theme-merbivore.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-merbivore.js',
   'DATA'),
  ('static\\ace\\theme-merbivore_soft.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-merbivore_soft.js',
   'DATA'),
  ('static\\ace\\theme-mono_industrial.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-mono_industrial.js',
   'DATA'),
  ('static\\ace\\theme-monokai.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-monokai.js',
   'DATA'),
  ('static\\ace\\theme-nord_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-nord_dark.js',
   'DATA'),
  ('static\\ace\\theme-one_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-one_dark.js',
   'DATA'),
  ('static\\ace\\theme-pastel_on_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-pastel_on_dark.js',
   'DATA'),
  ('static\\ace\\theme-solarized_dark.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-solarized_dark.js',
   'DATA'),
  ('static\\ace\\theme-solarized_light.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-solarized_light.js',
   'DATA'),
  ('static\\ace\\theme-sqlserver.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-sqlserver.js',
   'DATA'),
  ('static\\ace\\theme-terminal.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-terminal.js',
   'DATA'),
  ('static\\ace\\theme-textmate.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-textmate.js',
   'DATA'),
  ('static\\ace\\theme-tomorrow.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-tomorrow.js',
   'DATA'),
  ('static\\ace\\theme-tomorrow_night.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-tomorrow_night.js',
   'DATA'),
  ('static\\ace\\theme-tomorrow_night_blue.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-tomorrow_night_blue.js',
   'DATA'),
  ('static\\ace\\theme-tomorrow_night_bright.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-tomorrow_night_bright.js',
   'DATA'),
  ('static\\ace\\theme-tomorrow_night_eighties.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-tomorrow_night_eighties.js',
   'DATA'),
  ('static\\ace\\theme-twilight.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-twilight.js',
   'DATA'),
  ('static\\ace\\theme-vibrant_ink.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-vibrant_ink.js',
   'DATA'),
  ('static\\ace\\theme-xcode.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\theme-xcode.js',
   'DATA'),
  ('static\\ace\\worker-base.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-base.js',
   'DATA'),
  ('static\\ace\\worker-coffee.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-coffee.js',
   'DATA'),
  ('static\\ace\\worker-css.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-css.js',
   'DATA'),
  ('static\\ace\\worker-html.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-html.js',
   'DATA'),
  ('static\\ace\\worker-javascript.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-javascript.js',
   'DATA'),
  ('static\\ace\\worker-json.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-json.js',
   'DATA'),
  ('static\\ace\\worker-lua.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-lua.js',
   'DATA'),
  ('static\\ace\\worker-php.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-php.js',
   'DATA'),
  ('static\\ace\\worker-xml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-xml.js',
   'DATA'),
  ('static\\ace\\worker-xquery.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-xquery.js',
   'DATA'),
  ('static\\ace\\worker-yaml.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\ace\\worker-yaml.js',
   'DATA'),
  ('static\\adminCheck.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\adminCheck.js',
   'DATA'),
  ('static\\bootstrap-icons\\bootstrap-icons.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap-icons\\bootstrap-icons.css',
   'DATA'),
  ('static\\bootstrap-icons\\fonts\\bootstrap-icons.woff',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap-icons\\fonts\\bootstrap-icons.woff',
   'DATA'),
  ('static\\bootstrap-icons\\fonts\\bootstrap-icons.woff2',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap-icons\\fonts\\bootstrap-icons.woff2',
   'DATA'),
  ('static\\bootstrap\\css\\bootstrap.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap\\css\\bootstrap.min.css',
   'DATA'),
  ('static\\bootstrap\\css\\bootstrap.min.css.map',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap\\css\\bootstrap.min.css.map',
   'DATA'),
  ('static\\bootstrap\\js\\bootstrap.bundle.min.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap\\js\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\bootstrap\\js\\bootstrap.bundle.min.js.map',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootstrap\\js\\bootstrap.bundle.min.js.map',
   'DATA'),
  ('static\\bootswatch\\Cerulean.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Cerulean.min.css',
   'DATA'),
  ('static\\bootswatch\\Cosmo.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Cosmo.min.css',
   'DATA'),
  ('static\\bootswatch\\Cyborg.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Cyborg.min.css',
   'DATA'),
  ('static\\bootswatch\\Darkly.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Darkly.min.css',
   'DATA'),
  ('static\\bootswatch\\Default.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Default.min.css',
   'DATA'),
  ('static\\bootswatch\\Flatly.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Flatly.min.css',
   'DATA'),
  ('static\\bootswatch\\Journal.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Journal.min.css',
   'DATA'),
  ('static\\bootswatch\\Litera.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Litera.min.css',
   'DATA'),
  ('static\\bootswatch\\Lumen.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Lumen.min.css',
   'DATA'),
  ('static\\bootswatch\\Lux.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Lux.min.css',
   'DATA'),
  ('static\\bootswatch\\Materia.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Materia.min.css',
   'DATA'),
  ('static\\bootswatch\\Minty.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Minty.min.css',
   'DATA'),
  ('static\\bootswatch\\Morph.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Morph.min.css',
   'DATA'),
  ('static\\bootswatch\\Pulse.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Pulse.min.css',
   'DATA'),
  ('static\\bootswatch\\Quartz.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Quartz.min.css',
   'DATA'),
  ('static\\bootswatch\\Sandstone.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Sandstone.min.css',
   'DATA'),
  ('static\\bootswatch\\Simplex.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Simplex.min.css',
   'DATA'),
  ('static\\bootswatch\\Sketchy.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Sketchy.min.css',
   'DATA'),
  ('static\\bootswatch\\Slate.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Slate.min.css',
   'DATA'),
  ('static\\bootswatch\\Solar.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Solar.min.css',
   'DATA'),
  ('static\\bootswatch\\Spacelab.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Spacelab.min.css',
   'DATA'),
  ('static\\bootswatch\\Superhero.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Superhero.min.css',
   'DATA'),
  ('static\\bootswatch\\United.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\United.min.css',
   'DATA'),
  ('static\\bootswatch\\Vapor.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Vapor.min.css',
   'DATA'),
  ('static\\bootswatch\\Yeti.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Yeti.min.css',
   'DATA'),
  ('static\\bootswatch\\Zephyr.min.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\bootswatch\\Zephyr.min.css',
   'DATA'),
  ('static\\customAlert.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\customAlert.js',
   'DATA'),
  ('static\\favicon.ico',
   'E:\\letvar\\works\\python_app\\file_share\\static\\favicon.ico',
   'DATA'),
  ('static\\imageViewer\\imageViewer.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\imageViewer\\imageViewer.css',
   'DATA'),
  ('static\\imageViewer\\imageViewer.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\imageViewer\\imageViewer.js',
   'DATA'),
  ('static\\mediaViewer\\mediaViewer.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\mediaViewer\\mediaViewer.css',
   'DATA'),
  ('static\\mediaViewer\\mediaViewer.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\mediaViewer\\mediaViewer.js',
   'DATA'),
  ('static\\pdfViewer\\pdfViewer.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\pdfViewer\\pdfViewer.css',
   'DATA'),
  ('static\\pdfViewer\\pdfViewer.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\pdfViewer\\pdfViewer.js',
   'DATA'),
  ('static\\popperjs\\popper.min.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\popperjs\\popper.min.js',
   'DATA'),
  ('static\\preview.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\preview.js',
   'DATA'),
  ('static\\style.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\style.css',
   'DATA'),
  ('static\\workspace\\workspace.css',
   'E:\\letvar\\works\\python_app\\file_share\\static\\workspace\\workspace.css',
   'DATA'),
  ('static\\workspace\\workspace.js',
   'E:\\letvar\\works\\python_app\\file_share\\static\\workspace\\workspace.js',
   'DATA'),
  ('static\\zs.png',
   'E:\\letvar\\works\\python_app\\file_share\\static\\zs.png',
   'DATA'),
  ('templates\\dir_admin_login.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\dir_admin_login.html',
   'DATA'),
  ('templates\\directory.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\directory.html',
   'DATA'),
  ('templates\\directory.zip',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\directory.zip',
   'DATA'),
  ('templates\\directory_password.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\directory_password.html',
   'DATA'),
  ('templates\\error.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\error.html',
   'DATA'),
  ('templates\\global_password.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\global_password.html',
   'DATA'),
  ('templates\\header.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\header.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\index.html',
   'DATA'),
  ('templates\\ip_blocked.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\ip_blocked.html',
   'DATA'),
  ('templates\\share_directory.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\share_directory.html',
   'DATA'),
  ('templates\\share_manager.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\share_manager.html',
   'DATA'),
  ('templates\\share_password.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\share_password.html',
   'DATA'),
  ('templates\\share_view.html',
   'E:\\letvar\\works\\python_app\\file_share\\templates\\share_view.html',
   'DATA'),
  ('tkinterdnd2\\TkinterDnD.py',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\TkinterDnD.py',
   'DATA'),
  ('tkinterdnd2\\__init__.py',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\__init__.py',
   'DATA'),
  ('tkinterdnd2\\__pycache__\\TkinterDnD.cpython-312.pyc',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\__pycache__\\TkinterDnD.cpython-312.pyc',
   'DATA'),
  ('tkinterdnd2\\__pycache__\\__init__.cpython-312.pyc',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\libtkdnd2.9.3.so',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\libtkdnd2.9.3.so',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-arm64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\libtkdnd2.9.4.so',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\libtkdnd2.9.4.so',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\linux-x64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\libtkdnd2.9.3.dylib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\libtkdnd2.9.3.dylib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-arm64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\libtkdnd2.9.4.dylib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\libtkdnd2.9.4.dylib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\osx-x64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd2.9.3.lib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd2.9.3.lib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-arm64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd2.9.3.lib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd2.9.3.lib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd2.9.4.lib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd2.9.4.lib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_windows.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\pkgIndex.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd2.9.4.lib',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd2.9.4.lib',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_compat.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_generic.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_macosx.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_unix.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_utils.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x86\\tkdnd_windows.tcl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x86\\tkdnd_windows.tcl',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE.APACHE',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\REQUESTED',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE.BSD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\INSTALLER',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('pptx\\templates\\default.pptx',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\default.pptx',
   'DATA'),
  ('pptx\\templates\\docx-icon.emf',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\docx-icon.emf',
   'DATA'),
  ('pptx\\templates\\notesMaster.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\notesMaster.xml',
   'DATA'),
  ('pptx\\templates\\pptx-icon.emf',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\pptx-icon.emf',
   'DATA'),
  ('pptx\\templates\\theme.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\theme.xml',
   'DATA'),
  ('pptx\\templates\\xlsx-icon.emf',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\xlsx-icon.emf',
   'DATA'),
  ('pptx\\templates\\notes.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\notes.xml',
   'DATA'),
  ('pptx\\templates\\generic-icon.emf',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pptx\\templates\\generic-icon.emf',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\settings.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\settings.xml',
   'DATA'),
  ('docx\\templates\\default-styles.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-styles.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\fontTable.xml',
   'DATA'),
  ('docx\\templates\\default-footer.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-footer.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\thumbnail.jpeg',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\app.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\app.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\docProps\\core.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\docProps\\core.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\_rels\\item1.xml.rels',
   'DATA'),
  ('docx\\templates\\default-settings.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-settings.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\[Content_Types].xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\[Content_Types].xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\styles.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\styles.xml',
   'DATA'),
  ('docx\\templates\\default-comments.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-comments.xml',
   'DATA'),
  ('docx\\templates\\default-header.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-header.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\itemProps1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\customXml\\item1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\numbering.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\numbering.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\_rels\\document.xml.rels',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\theme\\theme1.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\stylesWithEffects.xml',
   'DATA'),
  ('docx\\templates\\default-docx-template\\_rels\\.rels',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\_rels\\.rels',
   'DATA'),
  ('docx\\templates\\default.docx',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default.docx',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\document.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\document.xml',
   'DATA'),
  ('docx\\py.typed',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\py.typed',
   'DATA'),
  ('docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\docx\\templates\\default-docx-template\\word\\webSettings.xml',
   'DATA'),
  ('pypinyin\\runner.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\runner.pyi',
   'DATA'),
  ('pypinyin\\contrib\\_tone_rule.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\style\\braille_mainland.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\braille_mainland.pyi',
   'DATA'),
  ('pypinyin\\contrib\\uv.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\uv.pyi',
   'DATA'),
  ('pypinyin\\style\\wadegiles.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\wadegiles.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\phrases_dict.pyi',
   'DATA'),
  ('pypinyin\\utils.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\utils.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_convert.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\_tone_convert.pyi',
   'DATA'),
  ('pypinyin\\__init__.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\_utils.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\_utils.pyi',
   'DATA'),
  ('pypinyin\\seg\\simpleseg.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\seg\\simpleseg.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_sandhi.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.pyi',
   'DATA'),
  ('pypinyin\\style\\gwoyeu.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\gwoyeu.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_convert.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\tone.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\tone.pyi',
   'DATA'),
  ('pypinyin\\style\\initials.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\initials.pyi',
   'DATA'),
  ('pypinyin\\core.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\core.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_rule.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\standard.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\standard.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.json',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\pinyin_dict.json',
   'DATA'),
  ('pypinyin\\contrib\\neutral_tone.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\neutral_tone.pyi',
   'DATA'),
  ('pypinyin\\converter.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\converter.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.json',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\phrases_dict.json',
   'DATA'),
  ('pypinyin\\constants.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\constants.pyi',
   'DATA'),
  ('pypinyin\\compat.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\compat.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\pinyin_dict.pyi',
   'DATA'),
  ('pypinyin\\phonetic_symbol.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\phonetic_symbol.pyi',
   'DATA'),
  ('pypinyin\\style\\_constants.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\_constants.pyi',
   'DATA'),
  ('pypinyin\\exceptions.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\exceptions.pyi',
   'DATA'),
  ('pypinyin\\style\\finals.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\finals.pyi',
   'DATA'),
  ('pypinyin\\style\\bopomofo.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\bopomofo.pyi',
   'DATA'),
  ('pypinyin\\style\\others.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\others.pyi',
   'DATA'),
  ('pypinyin\\seg\\mmseg.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\seg\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\tools\\toneconvert.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\tools\\toneconvert.pyi',
   'DATA'),
  ('pypinyin\\style\\cyrillic.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\cyrillic.pyi',
   'DATA'),
  ('pypinyin\\contrib\\mmseg.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\contrib\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\style\\__init__.pyi',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\style\\__init__.pyi',
   'DATA'),
  ('pypinyin\\py.typed',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\pypinyin\\py.typed',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tk_data\\license.terms',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tk_data\\button.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\images\\README',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tk_data\\console.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\tclIndex',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\text.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'E:\\letvar\\apps\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\LICENSE.md',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\LICENSE.md',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\top_level.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\entry_points.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\REQUESTED',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\WHEEL',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\REQUESTED',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('cheroot-10.0.1.dist-info\\METADATA',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\cheroot-10.0.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'E:\\letvar\\works\\python_app\\file_share\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'E:\\letvar\\works\\python_app\\file_share\\build\\main-onefile\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
