{% extends "header.html" %}
{% block content %}


    <div class="container py-5">
        <!-- Admin Status Bar -->
        <div class="row mb-4">
            <div class="col d-flex justify-content-between align-items-center">
                <h3 class="display-6 fw-bold text-primary">
                    <i class="bi bi-folder2-open me-2"></i>目录列表
                </h3>
                <div>
                    <button id="toggleViewBtn" class="btn btn-outline-info me-2" onclick="toggleView()">
                        <i class="bi bi-card-list"></i>
                    </button>
                    {% if session.admin %}
                        <div class="admin-controls d-inline">
                            <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addDirModal">
                                <i class="bi bi-plus-circle me-1"></i>添加目录
                            </button>
                            <button class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#settingsModal">
                                <i class="bi bi-gear me-1"></i>系统设置
                            </button>
                            <a class="btn btn-secondary me-2" href="/share-manager" target="_blank">
                                <i class="bi bi-share me-1"></i>私有分享
                            </a>
                            <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger">
                                <i class="bi bi-box-arrow-right me-1"></i>退出管理
                            </a>
                        </div>
                    {% else %}
                        <button class="btn btn-outline-primary" onclick="showAdminLogin()">
                            <i class="bi bi-shield-lock me-1"></i>管理
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Directory List -->
        <div id="directoryContainer">
            <div class="row g-4 card-view">
                {% for dir in dirs %}
                    <div class="col-md-4 col-lg-3 dir-item">
                        <div class="card h-100 shadow-sm hover-card">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0 text-truncate">
                                        <i class="bi bi-folder2 text-warning me-2"></i>
                                        {{ dir.alias }}
                                    </h5>
                                    {% if dir.password %}
                                        <i class="bi bi-lock-fill text-danger"></i>
                                    {% else %}
                                        <i class="bi bi-unlock text-success"></i>
                                    {% endif %}
                                </div>
                                {% if dir.desc %}
                                    <p class="card-text text-muted mb-3 small">{{ dir.desc }}</p>
                                {% endif %}
                                <div class="mt-auto">
                                    <a href="{{ url_for('list_dir', dirname=dir.alias) }}"
                                       class="btn btn-outline-info w-100 mb-2">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>进入目录
                                    </a>
                                    {% if session.admin %}
                                        <div class="btn-group w-100">
                                            <button class="btn btn-sm btn-outline-primary"
                                                    onclick="editDirectory('{{ dir.alias }}', '{{ dir.desc }}')">
                                                <i class="bi bi-pencil me-1"></i>编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteDirectory('{{ dir.alias }}')">
                                                <i class="bi bi-trash me-1"></i>删除
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <ul class="list-group list-view d-none">
                {% for dir in dirs %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-folder2 text-warning me-2"></i>
                            <strong>{{ dir.alias }}</strong>
                            {% if dir.password %}
                                <i class="bi bi-lock-fill text-danger ms-2"></i>
                            {% else %}
                                <i class="bi bi-unlock text-success ms-2"></i>
                            {% endif %}
                            {% if dir.desc %}
                                <p class="m-0 small text-muted">{{ dir.desc }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <a href="{{ url_for('list_dir', dirname=dir.alias) }}"
                               class="btn btn-sm btn-outline-info me-2">
                                <i class="bi bi-box-arrow-in-right me-1"></i>进入
                            </a>
                            {% if session.admin %}
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="editDirectory('{{ dir.alias }}', '{{ dir.desc }}')">
                                    <i class="bi bi-pencil me-1"></i>编辑
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="deleteDirectory('{{ dir.alias }}')">
                                    <i class="bi bi-trash me-1"></i>删除
                                </button>
                            {% endif %}
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <!-- 添加管理员登录模态框 -->
    <div class="modal fade" id="adminLoginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">管理员登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="adminLoginForm" onsubmit="return handleAdminLogin(event)">
                        <div class="mb-3">
                            <label class="form-label">管理密码</label>
                            <input type="password" class="form-control" id="adminPassword" required>
                            <div class="invalid-feedback" id="loginError"></div>
                        </div>
                        <button type="submit" class="btn btn-primary">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Directory Modal -->
    <div class="modal fade" id="addDirModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加共享目录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addDirForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">目录路径</label>
                            <input type="text" class="form-control" name="path" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">显示名称</label>
                            <input type="text" class="form-control" name="alias" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">访问密码（可选）</label>
                            <input type="password" class="form-control" name="password">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">目录管理密码（可选）</label>
                            <input type="password" class="form-control" name="admin_password">
                            <small class="text-muted">设置此目录的管理密码，拥有此密码的用户可以管理此目录。留空表示只有超级管理员可以管理</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">描述（可选）</label>
                            <textarea class="form-control" name="desc" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Directory Modal -->
    <div class="modal fade" id="editDirModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑目录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editDirForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">显示名称</label>
                            <input type="text" class="form-control" name="alias" id="editAlias" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">访问密码（可选）</label>
                            <input type="password" class="form-control" name="password">
                            <small class="text-muted">留空表示取消访问密码</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">目录管理密码（可选）</label>
                            <input type="password" class="form-control" name="admin_password" id="editAdminPassword">
                            <small class="text-muted">设置此目录的管理密码，拥有此密码的用户可以管理此目录。留空表示只有超级管理员可以管理</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">描述（可选）</label>
                            <textarea class="form-control" name="desc" id="editDesc" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">系统设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="settingsForm" onsubmit="updateSettings(event)">
                    <div class="modal-body">
                        <!-- 页面设置选项卡 -->
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="page-tab" data-bs-toggle="tab" data-bs-target="#page-settings" type="button" role="tab">
                                    <i class="bi bi-palette me-1"></i>页面设置
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security-settings" type="button" role="tab">
                                    <i class="bi bi-shield-lock me-1"></i>安全设置
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="settingsTabContent">
                            <!-- 页面设置 -->
                            <div class="tab-pane fade show active" id="page-settings" role="tabpanel">
                                <div class="mb-3">
                                    <label class="form-label">页面标题</label>
                                    <input type="text" class="form-control" id="pageTitle" name="page_title" value="{{ page_title }}">
                                    <small class="text-muted">显示在浏览器标签页上的标题</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Logo名称</label>
                                    <input type="text" class="form-control" id="logoName" name="logo_name" value="{{ logo_name }}">
                                    <small class="text-muted">左上角显示的Logo名称</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Logo图片</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="logoImageUrl" name="logo_image_url" value="{{ logo_image_url }}" placeholder="图片URL或留空">
                                        <button class="btn btn-outline-secondary" type="button" onclick="document.getElementById('logoFileInput').click()">
                                            <i class="bi bi-upload"></i> 上传
                                        </button>
                                    </div>
                                    <input type="file" id="logoFileInput" accept="image/*" style="display: none;" onchange="uploadLogo(this)">
                                    <small class="text-muted">支持本地上传或远程URL，推荐尺寸：高度30px</small>
                                    <div id="logoPreview" class="mt-2">
                                        {% if logo_image_url %}
                                            <img src="{% if logo_image_url.startswith('http') %}{{ logo_image_url }}{% else %}{{ url_for('static', filename=logo_image_url) }}{% endif %}"
                                                 alt="Logo预览" height="30" class="border rounded">
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- 安全设置 -->
                            <div class="tab-pane fade" id="security-settings" role="tabpanel">
                                <div class="mb-3">
                                    <label class="form-label">全局访问密码</label>
                                    <input type="password" class="form-control" name="global_password">
                                    <small class="text-muted">留空表示不需要密码</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">管理密码</label>
                                    <input type="password" class="form-control" name="admin_password">
                                    <small class="text-muted">留空表示不修改</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <style>
        .hover-card {
            transition: all 0.3s ease;
            border: none;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
        }

        .card-title {
            max-width: 80%;
        }

        .btn-primary {
            border-radius: 50px;
            padding: 8px 20px;
        }

        .display-6 {
            font-size: 2rem;
        }

        .list-group-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-view .btn-outline-primary, .list-view .btn-outline-danger {
            margin-left: 0.5rem;
        }
    </style>

    <script>
        const VIEW_MODE_KEY = 'viewMode';

        function toggleView() {
            const container = document.getElementById('directoryContainer');
            const cardView = container.querySelector('.card-view');
            const listView = container.querySelector('.list-view');

            cardView.classList.toggle('d-none');
            listView.classList.toggle('d-none');

            const currentView = cardView.classList.contains('d-none') ? 'list' : 'card';
            localStorage.setItem(VIEW_MODE_KEY, currentView);
        }

        function applyViewMode() {
            const savedViewMode = localStorage.getItem(VIEW_MODE_KEY);
            const container = document.getElementById('directoryContainer');
            const cardView = container.querySelector('.card-view');
            const listView = container.querySelector('.list-view');

            if (savedViewMode === 'list') {
                cardView.classList.add('d-none');
                listView.classList.remove('d-none');
            } else {
                cardView.classList.remove('d-none');
                listView.classList.add('d-none');
            }
        }

        document.addEventListener('DOMContentLoaded', applyViewMode);



        const API = {
            async addDirectory(formData) {
                const response = await fetch('/api/directory', {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) throw new Error('Failed to add directory');
                location.reload();
            },

            async updateDirectory(alias, formData) {
                const response = await fetch(`/api/directory/${alias}`, {
                    method: 'PUT',
                    body: formData
                });
                if (!response.ok) throw new Error('Failed to update directory');
                location.reload();
            },

            async deleteDirectory(alias) {
                const response = await fetch(`/api/directory/${alias}`, {
                    method: 'DELETE'
                });
                if (!response.ok) throw new Error('Failed to delete directory');
                location.reload();
            },

            async updateSettings(formData) {
                const response = await fetch('/api/settings', {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) throw new Error('Failed to update settings');
                location.reload();
            },

            async updatePageSettings(data) {
                const response = await fetch('/api/page-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update page settings');
                }
                return response.json();
            },

            async uploadLogo(file) {
                const formData = new FormData();
                formData.append('logo', file);

                const response = await fetch('/api/upload-logo', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to upload logo');
                }
                return response.json();
            }
        };

        // Directory Management Functions
        async function editDirectory(alias, desc) {
            const modal = new bootstrap.Modal(document.getElementById('editDirModal'));

            try {
                // 获取目录详细信息
                const response = await fetch(`/api/directory/${alias}`);
                if (response.ok) {
                    const dirInfo = await response.json();
                    document.getElementById('editAlias').value = dirInfo.alias;
                    document.getElementById('editDesc').value = dirInfo.desc;
                    document.getElementById('editAdminPassword').value = dirInfo.admin_password || '';
                    // 访问密码字段需要通过name属性获取
                    document.querySelector('input[name="password"]').value = dirInfo.password || '';
                } else {
                    // 如果获取失败，使用传入的参数
                    document.getElementById('editAlias').value = alias;
                    document.getElementById('editDesc').value = desc;
                }
            } catch (err) {
                // 如果获取失败，使用传入的参数
                document.getElementById('editAlias').value = alias;
                document.getElementById('editDesc').value = desc;
            }

            modal.show();

            document.getElementById('editDirForm').onsubmit = async (e) => {
                e.preventDefault();
                try {
                    await API.updateDirectory(alias, new FormData(e.target));
                    modal.hide();
                    location.reload();
                } catch (err) {
                    alert('修改失败: ' + err.message);
                }
            };
        }

        async function deleteDirectory(alias) {
            if (await FS_confirm('确定要删除这个分享目录吗？', '删除确认')) {
                try {
                    await API.deleteDirectory(alias);
                    location.reload();
                } catch (err) {
                    alert('删除失败: ' + err.message);
                }
            }
        }

        // Logo Upload Handler
        async function uploadLogo(input) {
            if (!input.files || !input.files[0]) return;

            const file = input.files[0];

            // 验证文件类型
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp'];
            if (!allowedTypes.includes(file.type)) {
                alert('请选择有效的图片文件（PNG、JPG、JPEG、GIF或BMP）');
                input.value = '';
                return;
            }

            // 验证文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
                alert('图片文件大小不能超过5MB');
                input.value = '';
                return;
            }

            try {
                const result = await API.uploadLogo(file);

                // 更新输入框和预览
                document.getElementById('logoImageUrl').value = result.path;
                updateLogoPreview(result.path);

                alert('Logo上传成功！');
            } catch (err) {
                alert('上传失败: ' + err.message);
                input.value = '';
            }
        }

        // Update Logo Preview
        function updateLogoPreview(imagePath) {
            const preview = document.getElementById('logoPreview');
            if (imagePath) {
                const isRemoteUrl = imagePath.startsWith('http');
                const src = isRemoteUrl ? imagePath : `/static/${imagePath}`;
                preview.innerHTML = `<img src="${src}" alt="Logo预览" height="30" class="border rounded">`;
            } else {
                preview.innerHTML = '';
            }
        }

        // Settings Management
        async function updateSettings(event) {
            event.preventDefault();

            // 获取当前活跃的标签页
            const activeTab = document.querySelector('#settingsTabs .nav-link.active').id;

            if (activeTab === 'page-tab') {
                // 处理页面设置
                const pageTitle = document.getElementById('pageTitle').value.trim();
                const logoName = document.getElementById('logoName').value.trim();
                const logoImageUrl = document.getElementById('logoImageUrl').value.trim();

                if (!pageTitle) {
                    alert('页面标题不能为空');
                    return;
                }

                if (!logoName) {
                    alert('Logo名称不能为空');
                    return;
                }

                try {
                    await API.updatePageSettings({
                        page_title: pageTitle,
                        logo_name: logoName,
                        logo_image_url: logoImageUrl
                    });

                    const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                    modal.hide();
                    alert('页面设置已更新');
                    location.reload();
                } catch (err) {
                    alert('更新页面设置失败: ' + err.message);
                }
            } else {
                // 处理安全设置（原有逻辑）
                const formData = new FormData(event.target);

                try {
                    await API.updateSettings(formData);
                    const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                    modal.hide();
                    alert('安全设置已更新');
                    location.reload();
                } catch (err) {
                    alert('更新安全设置失败: ' + err.message);
                }
            }
        }

        // Logo URL输入框变化时更新预览
        document.addEventListener('DOMContentLoaded', function() {
            const logoUrlInput = document.getElementById('logoImageUrl');
            if (logoUrlInput) {
                logoUrlInput.addEventListener('input', function() {
                    updateLogoPreview(this.value);
                });
            }
        });

        // Add Directory Form Handler
        document.getElementById('addDirForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                await API.addDirectory(new FormData(e.target));
                const modal = bootstrap.Modal.getInstance(document.getElementById('addDirModal'));
                modal.hide();
                location.reload();
            } catch (err) {
                alert('添加目录失败: ' + err.message);
            }
        });
    </script>

    <script src="/static/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/adminCheck.js"></script>

{% endblock %}
