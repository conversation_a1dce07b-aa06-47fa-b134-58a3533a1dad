{% extends "header.html" %}
{% block content %}
    {% set default_icon = 'file-earmark' %}
    {% set file_types = {
    'txt': 'file-text',
    'md': 'file-text',
    'json': 'file-code',
    'xml': 'file-code',
    'html': 'file-code',
    'css': 'file-code',
    'js': 'file-code',
    'py': 'file-code',
    'php': 'file-code',
    'pdf': 'file-pdf',
    'doc': 'file-word',
    'docx': 'file-word',
    'xls': 'file-excel',
    'xlsx': 'file-excel',
    'ppt': 'file-ppt',
    'pptx': 'file-ppt',
    'zip': 'file-zip',
    'rar': 'file-zip',
    '7z': 'file-zip',
    'gz': 'file-zip',
    'jpg': 'file-image',
    'jpeg': 'file-image',
    'png': 'file-image',
    'gif': 'file-image',
    'bmp': 'file-image',
    'svg': 'file-image',
    'mp3': 'file-music',
    'wav': 'file-music',
    'mp4': 'file-play',
    'avi': 'file-play',
    'mov': 'file-play'
} %}

    {% set ext_groups = {
    'code': ['py', 'js', 'java', 'cpp', 'c', 'php', 'rb', 'go', 'rs', 'swift'],
    'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
    'archive': ['zip', 'rar', '7z', 'gz', 'tar', 'bz2'],
    'document': ['doc', 'docx', 'pdf', 'txt', 'md', 'rtf'],
    'spreadsheet': ['xls', 'xlsx', 'csv'],
    'presentation': ['ppt', 'pptx'],
    'video': ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
    'audio': ['mp3', 'wav', 'ogg', 'flac', 'm4a']
} %}

    <script src="/static/ace/ace.js"></script>
    <script src="/static/ace/ext-language_tools.js"></script>
    <link href="/static/workspace/workspace.css" rel="stylesheet">
    <script src="/static/workspace/workspace.js"></script>

    <style>
        .vh-75 {
            height: 75vh !important;
        }

        .modal-xl {
            max-width: 90vw;
        }

        .CodeMirror {
            height: 100% !important;
        }

        .CodeMirror-toolbar {
            padding: 5px;
            background: #f7f7f7;
            border-bottom: 1px solid #ddd;
        }

        .CodeMirror-toolbar button {
            margin: 0 3px;
            padding: 3px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: white;
            cursor: pointer;
        }

        .CodeMirror-toolbar button:hover {
            background: #eee;
        }
    </style>


    <div class="container mt-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item bs-primary-border-subtle">
                    <a href="{{ url_for('index') }}">
                        <i class="bi bi-house"></i> 首页
                    </a>
                </li>
                {% for item in nav_path %}
                    <li class="breadcrumb-item  bs-primary-border-subtle {% if loop.last %}active{% endif %}">
                        {% if not loop.last %}
                            <a href="{{ url_for('list_dir', dirname=item.path) }}">{{ item.name }}</a>
                        {% else %}
                            {{ item.name }}
                        {% endif %}
                    </li>
                {% endfor %}
            </ol>
        </nav>

        {% if session.get('admin') %}
            <div class="mb-3">
                <button class="btn btn-primary me-2" onclick="showUploadDialog()">
                    <i class="bi bi-upload me-2"></i>上传文件
                </button>
                <button class="btn btn-success" onclick="showNewFolderDialog()">
                    <i class="bi bi-folder-plus me-2"></i>新建文件夹
                </button>
                <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger float-end">
                    <i class="bi bi-box-arrow-right me-2"></i>退出超级管理
                </a>
                {#                <a class="btn btn-secondary float-end" href="/share-manager/{{ dir_obj.path|extract_first_dir }}" target="_blank">#}
                <a class="btn btn-secondary float-end me-2" href="/share-manager/{{ dir_obj.alias }}" target="_blank">
                    <i class="bi bi-share me-2"></i>私有分享
                </a>

            </div>
        {% elif session.get('dir_admin_' + current_dir) %}
            <div class="mb-3">
                <button class="btn btn-primary me-2" onclick="showUploadDialog()">
                    <i class="bi bi-upload me-2"></i>上传文件
                </button>
                <button class="btn btn-success" onclick="showNewFolderDialog()">
                    <i class="bi bi-folder-plus me-2"></i>新建文件夹
                </button>
                <a href="{{ url_for('dir_admin_logout', dirname=current_dir) }}" class="btn btn-outline-warning float-end">
                    <i class="bi bi-box-arrow-right me-2"></i>退出目录管理
                </a>
                <span class="badge bg-warning text-dark float-end me-2 align-self-center">
                    <i class="bi bi-shield-check me-1"></i>目录管理员
                </span>
            </div>
        {% else %}
            <div class="mb-3">
                <button class="btn btn-outline-primary me-2" onclick="showAdminLogin()">
                    <i class="bi bi-shield-lock me-2"></i>超级管理员登录
                </button>
                {% if dir_obj.admin_password %}
                <button class="btn btn-outline-warning" onclick="showDirAdminLogin()">
                    <i class="bi bi-key me-2"></i>目录管理员登录
                </button>
                {% endif %}
            </div>
        {% endif %}

        {% if dir_obj.desc %}
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle me-2"></i>
                {{ dir_obj.desc }}
            </div>
        {% endif %}

        <div class="toolbar mb-3" id="batch-actions" style="display:none;">
            <button class="btn btn-primary" onclick="downloadSelected()">
                <i class="bi bi-download"></i> 打包下载
            </button>
            {% if session.get('admin') or session.get('dir_admin_' + current_dir) %}
            <button class="btn btn-primary" onclick="showMoveDialog()">
                <i class="bi bi-folder-symlink"></i> 移动到
            </button>
            {% endif %}
            <button class="btn btn-secondary" onclick="cancelSelection()">
                <i class="bi bi-x-circle"></i> 取消选择
            </button>
        </div>

        <div class="list-group">
            {% for item in items %}
                <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <input type="checkbox" class="form-check-input me-2 item-checkbox"
                               data-path="{{ item.path }}" data-name="{{ item.name }}"
                               data-is-dir="{{ item.is_dir|tojson|safe }}">
                        {% if item.is_dir %}
                            <a href="{{ url_for('list_dir', dirname=item.path) }}" class="item-link">
                                <i class="bi bi-folder folder-icon me-2"></i>
                                {{ item.name }}
                            </a>
                        {% else %}
                            <a href="#"
                               onclick="handleDownload('{{ current_dir }}', '{{ item.path.replace(current_dir + '/', '') }}'); return false;"
                               class="item-link">
                                {% set ext = item.name.split('.')[-1].lower() %}
                                {% set icon = file_types.get(ext) %}
                                {% if not icon %}
                                    {% for group, exts in ext_groups.items() %}
                                        {% if ext in exts %}
                                            {% if group == 'code' %}
                                                {% set icon = 'file-code' %}
                                            {% elif group == 'image' %}
                                                {% set icon = 'file-image' %}
                                            {% elif group == 'archive' %}
                                                {% set icon = 'file-zip' %}
                                            {% elif group == 'document' %}
                                                {% set icon = 'file-text' %}
                                            {% elif group == 'spreadsheet' %}
                                                {% set icon = 'file-excel' %}
                                            {% elif group == 'presentation' %}
                                                {% set icon = 'file-ppt' %}
                                            {% elif group == 'video' %}
                                                {% set icon = 'file-play' %}
                                            {% elif group == 'audio' %}
                                                {% set icon = 'file-music' %}
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                                <i class="bi bi-{{ icon or 'file-earmark' }} file-icon me-2"></i>
                                {{ item.name }}
                            </a>
                        {% endif %}
                    </div>

                    <div class="d-flex align-items-center">

                        <div class="item-actions me-3">
                            <!-- 分享按钮 -->
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="showShareDialog('{{ item.path }}', '{{ item.name }}')" title="分享">
                                <i class="bi bi-share"></i>
                            </button>
                            {% if not item.is_dir %}
                                <!-- 预览按钮 -->
                                {% set binary_extensions = ('exe', 'zip', 'rar', '7z', 'gz', 'dll', 'iso') %}
                                {% if not item.name.lower().endswith(binary_extensions) %}
                                    <button class="btn btn-sm btn-outline-info"
                                            onclick="{{ 'editFile' if (session.get('admin') or session.get('dir_admin_' + current_dir)) else
                                             'previewFile' }}('{{ item.name }}', '{{ item.path }}')" title="查看">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                {% endif %}
                            {% endif %}
                            {% if session.get('admin') or session.get('dir_admin_' + current_dir) %}
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="showMoveDialog(['{{ item.name }}'])" title="移动">
                                    <i class="bi bi-folder-symlink"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="renameItem('{{ item.name }}', {{ 'true' if item.is_dir else 'false' }})"
                                        title="改名">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="deleteItem('{{ item.name }}', {{ 'true' if item.is_dir else 'false' }})"
                                        title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            {% endif %}
                        </div>

                        <span class="text-muted">{{ item.size }}</span>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- 移动文件模态框 -->
    <div class="modal fade" id="moveDialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">移动文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">选择目标目录:</label>
                        <select class="form-select" id="moveTarget">
                            <option value="">加载中...</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="moveSelectedItems()">移动</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建分享链接模态框 -->
    <div class="modal fade" id="shareDialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建分享链接</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="shareForm">
                        <input type="hidden" id="sharePath">
                        <div class="mb-3">
                            <label class="form-label">管理密码</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="manage_code">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="generateRandomPassword('manage_code')">随机
                                </button>
                            </div>
                            <div class="form-text">你可以设一个自己记得住的密码用于以后管理这个分享链接</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">访问密码</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="sharePassword">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="generateRandomPassword('sharePassword')">随机
                                </button>
                            </div>
                            <div class="form-text">留空则不设置密码</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">有效期</label>
                            <select class="form-select" id="shareExpire">
                                <option value="1">1天</option>
                                <option value="7" selected>7天</option>
                                <option value="30">30天</option>
                                <option value="0">永久</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">链接描述</label>
                            <input type="text" class="form-control" id="desc">
                            <div class="form-text">关于链接的介绍，会显示在分享页面</div>
                        </div>
                    </form>
                    <div id="shareResult" class="d-none">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="shareUrl" readonly>
                            <button class="btn btn-outline-primary" onclick="copyShareUrl()">
                                复制链接
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="createShare()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员登录模态框 -->
    <div class="modal fade" id="adminLoginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">超级管理员登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="adminLoginForm" onsubmit="return handleAdminLogin(event)">
                        <div class="mb-3">
                            <label class="form-label">超级管理密码</label>
                            <input type="password" class="form-control" id="adminPassword" required>
                            <div class="invalid-feedback" id="loginError"></div>
                        </div>
                        <button type="submit" class="btn btn-primary">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 目录管理员登录模态框 -->
    <div class="modal fade" id="dirAdminLoginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">目录管理员登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dirAdminLoginForm" onsubmit="return handleDirAdminLogin(event)">
                        <input type="hidden" id="dirAdminDirname" value="{{ current_dir }}">
                        <div class="mb-3">
                            <label class="form-label">目录管理密码</label>
                            <input type="password" class="form-control" id="dirAdminPassword" required>
                            <div class="invalid-feedback" id="dirLoginError"></div>
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i> 输入此目录的管理密码以获取管理权限
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Dialog -->
    <div class="modal fade" id="uploadDialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="drop-zone" id="dropZone">
                        <p class="mb-2">拖拽文件到此处或点击选择文件</p>
                        <input type="file" class="form-control" id="fileInput" multiple style="display: none;">
                        <button class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                            选择文件
                        </button>
                    </div>
                    <div id="fileList" class="mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="startUpload()">上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- New Folder Dialog -->
    <div class="modal fade" id="newFolderDialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建文件夹</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="folderName" placeholder="文件夹名称" oninput="validateFolderNameInput()">
                        <div id="folderNameFeedback" class="form-text"></div>
                    </div>
                    <div class="alert alert-info small mb-0">
                        <strong>命名规则：</strong><br>
                        • 不能包含特殊字符: &lt; &gt; : " | ? * \ / # % &amp; { } $ ! ' @ + ` =<br>
                        • 不能使用系统保留名称（如CON、PRN等）<br>
                        • 不能以点开头或以点/空格结尾<br>
                        • 最多255个字符
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createFolder()">创建</button>
                </div>
            </div>
        </div>
    </div>


    <script src="/static/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/preview.js"></script>
    <script src="/static/adminCheck.js"></script>
    <script>
        const isAdmin = {{ 'true' if session.get('admin') else 'false' }};
        const isDirAdmin = {{ 'true' if session.get('dir_admin_' + current_dir) else 'false' }};
        const hasAdminPermission = isAdmin || isDirAdmin;

        // 全局变量
        let uploadFiles = []; // 存储待上传的文件
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');

        // 拖放上传相关代码
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            uploadFiles = e.dataTransfer.files;
            handleFiles(uploadFiles);
        });

        fileInput.addEventListener('change', () => {
            uploadFiles = fileInput.files;
            handleFiles(uploadFiles);
        });

        // 文件处理函数
        function handleFiles(files) {
            fileList.innerHTML = '';
            for (let file of files) {
                const item = document.createElement('div');
                item.className = 'mb-3';
                item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-1">
                <span>${file.name} (${formatFileSize(file.size)})</span>
                <span class="progress-text">0%</span>
            </div>
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>`;
                fileList.appendChild(item);
            }
        }

        function formatFileSize(bytes) {
            if (bytes >= 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            if (bytes >= 1024) return (bytes / 1024).toFixed(2) + ' KB';
            return bytes + ' B';
        }

        // 对话框显示函数
        function showUploadDialog() {
            fileList.innerHTML = '';
            fileInput.value = '';
            uploadFiles = [];
            new bootstrap.Modal(document.getElementById('uploadDialog')).show();
        }

        function showNewFolderDialog() {
            document.getElementById('folderName').value = '';
            new bootstrap.Modal(document.getElementById('newFolderDialog')).show();
        }

        // 文件操作函数
        async function startUpload() {
            if (!uploadFiles.length) {
                alert('请先选择要上传的文件');
                return;
            }

            const chunkSize = 1024 * 1024 * 2;
            const currentPath = window.location.pathname;

            for (let i = 0; i < uploadFiles.length; i++) {
                const file = uploadFiles[i];
                const fileElement = fileList.children[i];
                const progressBar = fileElement.querySelector('.progress-bar');
                const progressText = fileElement.querySelector('.progress-text');

                const chunks = Math.ceil(file.size / chunkSize);
                const fileId = Date.now().toString() + '_' + file.name;

                for (let chunk = 0; chunk < chunks; chunk++) {
                    const chunkData = file.slice(chunk * chunkSize, (chunk + 1) * chunkSize);
                    const formData = new FormData();
                    formData.append('file', chunkData);
                    formData.append('current_path', currentPath);
                    formData.append('chunk_index', chunk);
                    formData.append('total_chunks', chunks);
                    formData.append('identifier', fileId);
                    formData.append('filename', file.name);

                    try {
                        const response = await fetch(`/api/upload/{{ dir_obj.alias }}`, {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`Upload failed for ${file.name}`);
                        }

                        // 更新进度条
                        const progress = Math.round(((chunk + 1) / chunks) * 100);
                        progressBar.style.width = `${progress}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                        progressText.textContent = `${progress}%`;

                    } catch (error) {
                        console.error(`Error uploading ${file.name}:`, error);
                        progressBar.classList.add('bg-danger');
                        progressText.textContent = 'Failed';
                        return;
                    }
                }

                // 上传完成
                progressBar.classList.add('bg-success');
                progressText.textContent = 'Complete';
            }

            setTimeout(() => {
                location.reload();
            }, 1000);
        }


        // 文件夹名称验证函数
        function validateFolderName(name) {
            if (!name.trim()) return '文件夹名称不能为空';

            // 包含#号在内的所有问题字符
            const invalidChars = /[<>:"|?*\\\/#%&{}$!'@+`=]/g;
            const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];

            if (invalidChars.test(name)) {
                return '文件夹名称不能包含以下字符: < > : " | ? * \\ / # % & { } $ ! \' @ + ` =';
            }

            if (reservedNames.includes(name.toUpperCase())) {
                return '不能使用系统保留名称';
            }

            if (name.length > 255) return '文件夹名称过长（最多255个字符）';
            if (name.startsWith('.')) return '文件夹名称不能以点开头';
            if (name.endsWith('.') || name.endsWith(' ')) return '文件夹名称不能以点或空格结尾';

            return null;
        }

        // 自动修正功能
        function suggestFolderName(name) {
            return name.replace(/[<>:"|?*\\\/#%&{}$!'@+`=]/g, '_')
                      .replace(/^\.+/, '')
                      .replace(/[. ]+$/, '')
                      .substring(0, 255);
        }

        // 实时验证输入
        function validateFolderNameInput() {
            const input = document.getElementById('folderName');
            const feedback = document.getElementById('folderNameFeedback');
            const createBtn = document.querySelector('#newFolderDialog .btn-primary');

            const name = input.value.trim();
            const error = validateFolderName(name);

            if (!name) {
                feedback.textContent = '';
                feedback.className = 'form-text';
                input.className = 'form-control';
                createBtn.disabled = false;
            } else if (error) {
                feedback.textContent = error;
                feedback.className = 'form-text text-danger';
                input.className = 'form-control is-invalid';
                createBtn.disabled = true;

                // 显示建议名称
                const suggested = suggestFolderName(name);
                if (suggested && suggested !== name) {
                    feedback.innerHTML = `${error}<br><small class="text-muted">建议: ${suggested} <a href="#" onclick="applySuggestion('${suggested}'); return false;" class="text-primary">使用建议</a></small>`;
                }
            } else {
                feedback.textContent = '✓ 文件夹名称有效';
                feedback.className = 'form-text text-success';
                input.className = 'form-control is-valid';
                createBtn.disabled = false;
            }
        }

        // 应用建议名称
        function applySuggestion(suggested) {
            document.getElementById('folderName').value = suggested;
            validateFolderNameInput();
        }

        async function createFolder() {
            const folderName = document.getElementById('folderName').value.trim();
            if (!folderName) return;

            // 验证文件夹名称
            const error = validateFolderName(folderName);
            if (error) {
                const suggested = suggestFolderName(folderName);
                const message = `${error}\n\n建议使用: ${suggested}\n\n是否使用建议的名称？`;

                if (confirm(message)) {
                    document.getElementById('folderName').value = suggested;
                    return;
                } else {
                    return;
                }
            }

            const currentPath = window.location.pathname;
            const formData = new FormData();
            formData.append('name', folderName);
            formData.append('current_path', currentPath);

            try {
                const response = await fetch(`/api/mkdir/{{ dir_obj.alias }}`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    location.reload();
                } else {
                    const errorText = await response.text();
                    alert('创建文件夹失败: ' + errorText);
                }
            } catch (error) {
                console.error('Error creating folder:', error);
                alert('创建文件夹时发生错误');
            }
        }

        function renameItem(oldName, isDir) {
            const itemType = isDir ? '文件夹' : '文件';
            let newName = prompt(`请输入新的${itemType}名称:`, oldName);
            if (!newName || newName === oldName) return;

            // 如果是文件夹，验证名称
            if (isDir) {
                const error = validateFolderName(newName);
                if (error) {
                    const suggested = suggestFolderName(newName);
                    const message = `${error}\n\n建议使用: ${suggested}\n\n是否使用建议的名称？`;

                    if (confirm(message)) {
                        newName = suggested;
                    } else {
                        return;
                    }
                }
            }

            const currentPath = window.location.pathname;
            const formData = new FormData();
            formData.append('old_name', oldName);
            formData.append('new_name', newName);
            formData.append('current_path', currentPath);
            formData.append('is_dir', isDir);

            fetch(`/api/rename/{{ dir_obj.alias }}`, {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        return response.text().then(errorMsg => {
                            throw new Error(errorMsg);
                        });
                    }
                })
                .catch(error => {
                    alert(`${itemType}重命名失败: ${error.message}`);
                });
        }

        async function deleteItem(name, isDir) {
            const itemType = isDir ? '文件夹' : '文件';

            const confirmed = await FS_confirm(`确定要删除${itemType} "${name}" 吗？${isDir ? '\n注意：文件夹必须为空才能删除' : ''}`, '删除确认');
            if (!confirmed) return;

            const currentPath = window.location.pathname;
            const formData = new FormData();
            formData.append('name', name);
            formData.append('current_path', currentPath);
            formData.append('is_dir', isDir);

            fetch(`/api/delete/{{ dir_obj.alias }}`, {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        // 获取详细错误信息
                        return response.text().then(errorMsg => {
                            throw new Error(errorMsg);
                        });
                    }
                })
                .catch(error => {
                    alert(`${itemType}删除失败: ${error.message}`);
                });
        }


        function handleDownload(dirname, filename) {
            window.location.href = `/download/${dirname}/${filename}`;
        }

        function downloadSelected() {
            const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked')).map(checkbox => ({
                path: checkbox.dataset.path,
                name: checkbox.dataset.name
            }));

            if (selectedItems.length === 0) {
                alert('请选择要下载的文件');
                return;
            }

            fetch('/api/batch-download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({items: selectedItems})
            }).then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Download failed');
            })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'selected_files.zip';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                })
                .catch(error => {
                    alert('下载失败: ' + error.message);
                });
        }

        // 获取文件图标的辅助函数
        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            // 使用与原始模板相同的图标映射逻辑
            const fileTypes = {{ file_types|tojson }};
            const extGroups = {{ ext_groups|tojson }};

            if (fileTypes[ext]) return fileTypes[ext];

            for (const [group, exts] of Object.entries(extGroups)) {
                if (exts.includes(ext)) {
                    switch (group) {
                        case 'code':
                            return 'file-code';
                        case 'image':
                            return 'file-image';
                        case 'archive':
                            return 'file-zip';
                        case 'document':
                            return 'file-text';
                        case 'spreadsheet':
                            return 'file-excel';
                        case 'presentation':
                            return 'file-ppt';
                        case 'video':
                            return 'file-play';
                        case 'audio':
                            return 'file-music';
                    }
                }
            }

            return 'file-earmark';
        }

        // 如果这两个函数在原始代码中没有定义,需要添加它们的定义 :
        function bindCheckboxEvents() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                    document.getElementById('batch-actions').style.display =
                        checkedBoxes.length > 0 ? 'block' : 'none';
                });
            });
        }

        function initTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const searchInput = document.getElementById('fileSearch');
            const clearBtn = searchInput.parentElement.querySelector('.btn-clear');
            const searchBtn = document.getElementById('searchBtn');
            const listGroup = document.querySelector('.list-group');
            const items = document.querySelectorAll('.list-group-item');

            // 保存原始列表HTML
            const originalListHTML = listGroup.innerHTML;

            // 检查是否存在子目录
            // 使用items数组直接判断是否有子目录
            const hasSubDirs =
            {{ items|tojson }}.
            some(item => item.is_dir);

            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase();
                const listGroup = document.querySelector('.list-group');

                // 搜索框为空时恢复原始列表
                if (searchTerm === '') {
                    listGroup.innerHTML = originalListHTML;
                    bindCheckboxEvents();
                    initTooltips();
                    return;
                }

                if (!hasSubDirs) {
                    // 纯前端搜索
                    const allItems = listGroup.querySelectorAll('.list-group-item');

                    allItems.forEach(item => {
                        // 使用 display 样式直接控制可见性
                        const fileName = item.querySelector('.item-link').textContent.trim();
                        const matches = fileName.toLowerCase().includes(searchTerm);

                        // 强制设置display属性
                        item.setAttribute('style', matches ? 'display: flex !important' : 'display: none !important');
                    });
                } else {
                    const currentAlias = '{{ dir_obj.alias }}';
                    fetch(`/api/search/${currentAlias}?term=${searchTerm}`)
                        .then(response => response.json())
                        .then(results => {
                            // 清空现有列表
                            listGroup.innerHTML = '';

                            // 根据搜索结果创建新的列表项
                            results.forEach(item => {
                                const listItem = document.createElement('div');
                                listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

                                const icon = item.is_dir ? 'folder' : getFileIcon(item.name);

                                listItem.innerHTML = `
    <div class="d-flex align-items-center">
        <input type="checkbox" class="form-check-input me-2 item-checkbox"
               data-path="${item.path}" data-name="${item.name}" data-is-dir="${item.is_dir}">
        <a href="${item.is_dir ? '/dir/' + item.path : '#'}"
           class="item-link"
           ${!item.is_dir ? `onclick="handleDownload('${currentAlias}', '${item.path}'); return false;"` : ''}>
            <i class="bi bi-${icon} me-2"></i>
            ${item.name}
        </a>
    </div>
    <div class="d-flex align-items-center">
        <div class="item-actions me-3">
            <button class="btn btn-sm btn-outline-success"
                    onclick="showShareDialog('${item.path}', '${item.name}')" title="分享">
                <i class="bi bi-share"></i>
            </button>
            ${!item.is_dir ? `
                <button class="btn btn-sm btn-outline-info"
                        onclick="${hasAdminPermission ? 'editFile' : 'previewFile'}('${item.name}', '${item.path}')"
                        title="查看">
                    <i class="bi bi-eye"></i>
                </button>
            ` : ''}
            ${hasAdminPermission ? `
                <button class="btn btn-sm btn-outline-primary"
                        onclick="showMoveDialog('${item.name}')" title="移动">
                    <i class="bi bi-folder-symlink"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary"
                        onclick="renameItem('${item.name}', ${item.is_dir})" title="改名">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger"
                        onclick="deleteItem('${item.name}', ${item.is_dir})" title="删除">
                    <i class="bi bi-trash"></i>
                </button>
            ` : ''}
        </div>
        ${item.size ? `<span class="text-muted">${item.size}</span>` : ''}
    </div>
`;

                                listGroup.appendChild(listItem);
                            });
                            // 重新绑定checkbox事件
                            bindCheckboxEvents();
                            // 重新初始化工具提示
                            initTooltips();
                        });
                }
            }

            // 显示/隐藏清除按钮
            searchInput.addEventListener('input', () => {
                clearBtn.style.display = searchInput.value ? 'block' : 'none';
            });

            // 清除搜索内容
            clearBtn.addEventListener('click', () => {
                searchInput.value = '';
                clearBtn.style.display = 'none';
                performSearch(); // 触发搜索函数以恢复原始列表
            });

            // 点击搜索按钮时执行搜索
            searchBtn.addEventListener('click', performSearch);

            // 回车时也执行搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
        /* 移动文件逻辑 */
        // 显示移动对话框
        function showMoveDialog(singleItem = null) {
            if (!hasAdminPermission) {
                alert('需要管理员权限');
                return;
            }
            const selectedItems = singleItem ?
                [{name: singleItem, path: singleItem}] :
                Array.from(document.querySelectorAll('.item-checkbox:checked')).map(checkbox => ({
                    name: checkbox.dataset.name,
                    path: checkbox.dataset.path,
                    is_dir: checkbox.dataset.isDir === 'true'
                }));

            if (selectedItems.length === 0) {
                alert('请选择要移动的文件');
                return;
            }

            // 获取当前路径
            const currentPath = window.location.pathname;

            // 初始化目标目录选择器
            initializeTargetDirectories(currentPath);

            // 确保数据格式正确
            const sanitizedItems = selectedItems.map(item => ({
                name: typeof item.name === 'string' ? item.name : item.name[0],
                path: typeof item.path === 'string' ? item.path : item.path[0],
                is_dir: !!item.is_dir
            }));

            // 保存选中的项目到dialog中
            const dialog = document.getElementById('moveDialog');
            dialog.dataset.selectedItems = JSON.stringify(sanitizedItems);

            // 显示对话框
            new bootstrap.Modal(dialog).show();
        }

        // 初始化目标目录选择器
        function initializeTargetDirectories(currentPath) {
            const select = document.getElementById('moveTarget');
            select.innerHTML = '<option value="">加载中...</option>';

            // 获取所有目录结构
            fetch(`/api/directories/{{ dir_obj.alias }}`)
                .then(response => response.json())
                .then(directories => {
                    select.innerHTML = '';
                    // 添加根目录选项
                    select.innerHTML += `<option value="/dir/{{ dir_obj.alias }}">/</option>`;

                    // 添加其他目录选项
                    directories.forEach(dir => {
                        if (dir.path !== currentPath) { // 排除当前目录
                            select.innerHTML += `<option value="${dir.path}">${dir.name}</option>`;
                        }
                    });
                })
                .catch(error => {
                    console.error('Error loading directories:', error);
                    select.innerHTML = '<option value="">加载失败</option>';
                });
        }

        // 执行移动操作
        function moveSelectedItems() {
            const dialog = document.getElementById('moveDialog');
            const selectedItems = JSON.parse(dialog.dataset.selectedItems || '[]');
            const targetPath = document.getElementById('moveTarget').value;
            const currentPath = window.location.pathname;

            if (!targetPath) {
                alert('请选择目标目录');
                return;
            }

            // 发送移动请求
            fetch(`/api/move/{{ dir_obj.alias }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    items: selectedItems,
                    target_path: targetPath,
                    current_path: currentPath
                })
            })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        return response.text().then(text => {
                            throw new Error(text);
                        });
                    }
                })
                .catch(error => {
                    alert('移动失败: ' + error.message);
                });
        }

        /* 移动文件逻辑 end */
    </script>
    <!--通用footer开始-->
{% endblock %}
