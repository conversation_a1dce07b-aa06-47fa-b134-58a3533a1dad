#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试logo路径修复的脚本
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import get_app_path, cleanup_old_logos

def test_logo_paths():
    """测试logo路径配置"""
    print("=== Logo路径测试 ===")
    
    # 测试get_app_path函数
    app_path = get_app_path()
    temp_path = get_app_path(tempdir=True)
    
    print(f"程序运行目录: {app_path}")
    print(f"临时文件目录: {temp_path}")
    
    # 测试logo目录路径
    logo_dir = os.path.join(app_path, "static", "logos")
    print(f"Logo目录: {logo_dir}")
    
    # 创建测试目录
    os.makedirs(logo_dir, exist_ok=True)
    print(f"Logo目录是否存在: {os.path.exists(logo_dir)}")
    
    # 测试清理函数
    print("\n=== 测试清理函数 ===")
    
    # 创建一些测试文件
    test_files = ["test1.png", "test2.jpg", "current.png"]
    for file in test_files:
        test_path = os.path.join(logo_dir, file)
        with open(test_path, 'w') as f:
            f.write("test")
        print(f"创建测试文件: {file}")
    
    print(f"清理前文件数量: {len(os.listdir(logo_dir))}")
    
    # 测试清理，保留current.png
    cleanup_old_logos(logo_dir, "current.png")
    
    remaining_files = os.listdir(logo_dir)
    print(f"清理后文件数量: {len(remaining_files)}")
    print(f"剩余文件: {remaining_files}")
    
    # 清理测试文件
    for file in remaining_files:
        os.remove(os.path.join(logo_dir, file))
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_logo_paths()
